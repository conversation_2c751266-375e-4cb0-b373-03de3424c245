<template>
  <view>
    <!-- 设置弹窗 -->
    <uni-popup ref="settingsPopup" type="center">
      <view class="settings-popup">
        <view class="settings-header">
          <text class="settings-title">{{ $t('直播设置') }}</text>
          <view class="close-btn" @click="closeSettingsPopup">
            <uni-icons type="close" size="24" color="#ffffff"></uni-icons>
          </view>
        </view>

        <view class="settings-content">
          <view class="settings-item">
            <text class="text">{{ $t('麦克风') }}</text>
            <switch :checked="isAudioEnabled" @change="handleAudioToggle" color="#FF3B5C"></switch>
          </view>

          <view class="settings-item">
            <text class="text">{{ $t('摄像头') }}</text>
            <switch :checked="isVideoEnabled" @change="handleVideoToggle" color="#FF3B5C"></switch>
          </view>

          <view class="settings-item">
            <text class="text">{{ $t('切换摄像头') }}</text>
            <button class="small-btn" @click="handleSwitchCamera">
              <uni-icons type="loop" size="20" color="#FF3B5C"></uni-icons>
            </button>
          </view>

          <view class="settings-item">
            <text class="text">{{ $t('美颜设置') }}</text>
            <button class="small-btn" @click="openBeautySettings">
              <uni-icons type="star" size="20" color="#FF3B5C"></uni-icons>
            </button>
          </view>

          <view class="settings-item">
            <text class="text">{{ $t('允许评论') }}</text>
            <switch :checked="info.allowComments === 1" @change="toggleComments" color="#FF3B5C"></switch>
          </view>

          <view class="settings-item">
            <text class="text">{{ $t('允许送礼物') }}</text>
            <switch :checked="info.allowGifts === 1" @change="toggleGifts" color="#FF3B5C"></switch>
          </view>

          <view class="settings-item">
            <text class="text">{{ $t('允许连麦') }}</text>
            <text class="text small">{{ $t('连麦积分') }}:{{ info.lianmaiScore || 0 }}</text>
            <switch :checked="info.allowLianmai === 1" @change="toggleLianMai" color="#FF3B5C"></switch>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 连麦请求弹窗 -->
    <uni-popup ref="lianmaiRequestPopup" type="dialog">
      <uni-popup-dialog
          :title="$t('连麦请求')"
          :content="lianmaiRequestContent"
          :before-close="true"
          @confirm="handleLianmaiConfirm"
          @close="handleLianmaiReject"
          confirmText="同意"
          cancelText="拒绝"
      ></uni-popup-dialog>
    </uni-popup>
    
    <!-- 连麦积分设置弹窗 -->
    <lianmai-score-popup ref="lianmaiScorePopup"></lianmai-score-popup>
  </view>
</template>

<script>
import i18n from "@/Lib/i18n";
import LianmaiScorePopup from "./LianmaiScorePopup";
import {
  AppsLiveApiLiveRoomsApiSwitchCommentsSocketOff,
  AppsLiveApiLiveRoomsApiSwitchCommentsSocketOn,
  AppsLiveApiLiveRoomsApiSwitchCommentsSocketSend,
  AppsLiveApiLiveRoomsApiSwitchGiftSocketOff,
  AppsLiveApiLiveRoomsApiSwitchGiftSocketOn,
  AppsLiveApiLiveRoomsApiSwitchGiftSocketSend,
  AppsLiveApiLiveRoomsApiSwitchLianMaiSocketOff,
  AppsLiveApiLiveRoomsApiSwitchLianMaiSocketOn,
  AppsLiveApiLiveRoomsApiSwitchLianMaiSocketSend,
  AppsLiveApiLiveRoomsApiRequestLianMaiSocketOn,
  AppsLiveApiLiveRoomsApiRequestLianMaiSocketOff,
  AppsLiveApiLiveRoomsApiReplyLianMaiSocketSend
} from "@/network/api/live-rooms";


export default {
  name: "SettingsPopup",
  components: {
    LianmaiScorePopup
  },
  props: {
    roomInfo: {
      type: Object,
      default: {
        allowComments: 1,
        allowGifts: 1
      }
    }
  },
  data() {
    return {
      isAudioEnabled: true,
      isVideoEnabled: true,
      info: {},
      lianmaiRequestContent: '',
      lianmaiRequestUser: null,
    }
  },
  created() {
    this.info = this.roomInfo;
    uni.$on('openSettingsPopup', this.open)
    AppsLiveApiLiveRoomsApiSwitchCommentsSocketOn(true, this.handledSwitchComments)
    AppsLiveApiLiveRoomsApiSwitchGiftSocketOn(true, this.handledSwitchGift)
    AppsLiveApiLiveRoomsApiSwitchLianMaiSocketOn(true, this.handledSwitchLianMai)
    // 添加连麦请求监听
    AppsLiveApiLiveRoomsApiRequestLianMaiSocketOn(true, this.handleLianmaiRequest)
  },
  mounted() {

  },
  unmounted() {
    uni.$off('openSettingsPopup', this.open)
    AppsLiveApiLiveRoomsApiSwitchCommentsSocketOff(true, this.handledSwitchComments)
    AppsLiveApiLiveRoomsApiSwitchGiftSocketOff(true, this.handledSwitchGift)
    AppsLiveApiLiveRoomsApiSwitchLianMaiSocketOff(true, this.handledSwitchLianMai)

    // 移除连麦请求监听
    AppsLiveApiLiveRoomsApiRequestLianMaiSocketOff(this.handleLianmaiRequest)
  },
  methods: {
    $t(text) {
      return i18n.t(text);
    },
    handleAudioToggle(e) {
      const value = e.detail.value;
      this.isAudioEnabled = value;
      uni.$emit('toggleAudio', value);
    },
    handleVideoToggle(e) {
      const value = e.detail.value;
      this.isVideoEnabled = value;
      uni.$emit('toggleVideo', value);
    },
    handleSwitchCamera() {
      uni.$emit('switchCamera');
    },
    openBeautySettings() {
      uni.$emit('openBeautySettings');
    },
    toggleComments(e) {
      const value = e.detail.value ? 1 : 0;
      AppsLiveApiLiveRoomsApiSwitchCommentsSocketSend({
        id: this.info.id,
        allowComments: value
      })
    },
    handledSwitchComments(res) {
      console.log('handledSwitchComments:' + res)
      const value = res.allowComments;
      this.info.allowComments = value;
      uni.showToast({
        title: value ? this.$t('已允许评论') : this.$t('已禁止评论'),
        icon: 'none'
      });
    },
    handledSwitchGift(res) {
      const value = res.allowGifts;
      this.info.allowGifts = value;
      uni.showToast({
        title: value ? this.$t('已允许礼物') : this.$t('已禁止礼物'),
        icon: 'none'
      });
    },
    handledSwitchLianMai(res) {
      const value = res.allowLianmai;
      this.info.allowLianmai = res.allowLianmai;
      this.info.lianmaiScore = res.lianmaiScore;
      uni.showToast({
        title: value ? this.$t('已允许连麦') : this.$t('已禁止连麦'),
        icon: 'none'
      });
    },
    toggleGifts(e) {
      const value = e.detail.value ? 1 : 0;
      AppsLiveApiLiveRoomsApiSwitchGiftSocketSend({
        id: this.info.id,
        allowGifts: value
      })
    },
    toggleLianMai(e) {
      const value = e.detail.value ? 1 : 0;

      if (value) {
        // 使用自定义连麦积分弹窗
        this.$refs.lianmaiScorePopup.open((confirmed, score) => {
          if (confirmed) {
            this.sendLianMai(1, score);
          } else {
            // 用户取消，将开关状态恢复
            this.info.allowLianmai = 0;
          }
        });
      } else {
        this.sendLianMai(0, 0);
      }
    },
    sendLianMai(value, score) {
      AppsLiveApiLiveRoomsApiSwitchLianMaiSocketSend({
        id: this.info.id,
        allowLianmai: value,
        lianmaiScore: score
      })
    },

    // 处理连麦请求
    handleLianmaiRequest(res) {
      console.log('handleLianMaiRequest:')
      console.log(res)
      if (res && res.user) {
        this.lianmaiRequestContent = `${res.user.nickname} ${this.$t('请求与您连麦，是否同意？')}`;
        this.lianmaiRequestUser = res;
        this.$refs.lianmaiRequestPopup.open('center');
      }
    },

    // 同意连麦请求
    handleLianmaiConfirm() {
      if (this.lianmaiRequestUser) {
        this.repLianMai(1);
        uni.showToast({
          title: this.$t('已同意连麦请求'),
          icon: 'success'
        });
      }
      this.$refs.lianmaiRequestPopup.close();
    },

    // 拒绝连麦请求
    handleLianmaiReject() {
      if (this.lianmaiRequestUser) {
        this.repLianMai(0);

        uni.showToast({
          title: this.$t('已拒绝连麦请求'),
          icon: 'none'
        });
      }
      this.$refs.lianmaiRequestPopup.close();
    },

    repLianMai(allowLianMai) {
      AppsLiveApiLiveRoomsApiReplyLianMaiSocketSend({
        id: this.info.id,
        allowLianmai: allowLianMai,
        lianmaiUserId: this.lianmaiRequestUser.lianmaiUserId
      });
    },

    open() {
      this.$refs.settingsPopup.open('center');
    },
    closeSettingsPopup() {
      this.$refs.settingsPopup.close();
    }
  }
}
</script>


<style scoped lang="scss">

/* 设置弹窗样式 */
.settings-popup {
  width: 600rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20rpx;
  padding: 30rpx;

  .settings-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .settings-title {
      font-size: 18px;
      font-weight: bold;
      color: #ffffff;
    }

    .close-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .settings-content {
    padding-top: 20rpx;


    .settings-item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom-width: 1rpx;
      border-bottom-style: solid;
      border-bottom-color: rgba(255, 255, 255, 0.05);
    }

    .settings-item:last-child {
      border-bottom-width: 0;
    }

    .text {
      font-size: 16px;
      color: #ffffff;

      &.small {
        font-size: 12px;
      }
    }

    .small-btn {
      background-color: #ffffff;
      width: 105rpx;
      height: 66rpx;
      border-radius: 66rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10rpx;
    }
  }


}
</style>