<template>
  <!-- 主播视频预览区域 -->
  <view id="publisher" class="publisher"></view>
</template>
<script>
import {createVideo, getVideoSrcObjectStream} from "@/components/live/js/live.tool";
import {SrsRtcPublisherAsync} from "@/components/live/js/srs.sdk";
import {
  AppsLiveApiLiveRoomsApiOfflineSocketSend,
  AppsLiveApiLiveRoomsApiStartSocketSend
} from "@/network/api/live-rooms";
import {Config} from "@/Config";
import {User} from "@/Lib/User";
import {BeautyFilter} from "@/components/live/js/beauty-filter";

export default {
  name: "publisher",
  props: {
    roomId: {
      type: Number,
      default: 0
    },
    display: {
      type: Number,
      default: ''
    },
    isPush: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 视频元素 主播播放器
      videoElement: null,

      // 直播流和信令
      publisher: null,
      stream: null,
      originalStream: null, // 保存原始流，用于美颜处理
      sig: null,

      isLiving: false,

      // 媒体状态
      isAudioEnabled: true,
      isVideoEnabled: true,
      currentFacingMode: 'user',

      // 美颜相关
      beautyFilter: null,
      beautyEnabled: false,
      beautySettings: {
        smoothing: 0.5,
        whitening: 0.3,
        brightness: 0.1,
        contrast: 0.1,
        saturation: 0.1,
        sharpening: 0.2
      }
    }
  },
  created() {
    uni.$on('switchCamera', this.switchCamera)
    uni.$on('startLive', this.startLive)
    uni.$on('toggleAudio', this.toggleAudio)
    uni.$on('toggleVideo', this.toggleVideo)
    uni.$on('stopLive', this.stopLive)
    uni.$on('beautySettingsChange', this.handleBeautySettingsChange)

    // 初始化美颜滤镜，优先使用WebGL版本，失败则使用简化版本
    try {
      this.beautyFilter = new BeautyFilter();
    } catch (error) {
      console.warn('WebGL美颜滤镜初始化失败，使用简化版本:', error);
      this.beautyFilter = new SimpleBeautyFilter();
    }
  },
  mounted() {
    setTimeout(() => {
      this.initVideoPreview();
    }, 500)

  },
  unmounted() {
    uni.$off('switchCamera', this.switchCamera)
    uni.$off('startLive', this.startLive)
    uni.$off('toggleAudio', this.toggleAudio)
    uni.$off('toggleVideo', this.toggleVideo)
    uni.$off('stopLive', this.stopLive)
    uni.$off('beautySettingsChange', this.handleBeautySettingsChange)

    // 销毁美颜滤镜资源
    if (this.beautyFilter) {
      this.beautyFilter.destroy();
    }
  },
  methods: {

    // 初始化视频预览
    initVideoPreview() {
      // 使用createVideo方法创建视频元素，并传入样式选项
      this.videoElement = createVideo('publisher', {
        objectFit: 'cover',
        width: '100%',
        height: '100%',
        position: 'absolute'
      });
      this.startCameraPreview();
    },


    async startCameraPreview() {
      try {
        // 获取原始摄像头流
        this.originalStream = await getVideoSrcObjectStream(this.currentFacingMode);

        // 根据美颜设置决定使用哪个流
        if (this.beautyEnabled && this.beautyFilter) {
          this.beautyFilter.setEnabled(true);
          this.beautyFilter.updateSettings(this.beautySettings);

          try {
            this.stream = await getBeautyVideoSrcObjectStream(this.currentFacingMode, 720, this.beautyFilter);
          } catch (error) {
            console.error('美颜流创建失败，使用原始流:', error);
            this.stream = this.originalStream;
            uni.showToast({
              title: '美颜功能暂不可用',
              icon: 'none'
            });
          }
        } else {
          this.stream = this.originalStream;
        }

        this.videoElement.srcObject = this.stream;
      } catch (error) {
        console.error('启动摄像头预览失败:', error);
        uni.showToast({
          title: '摄像头启动失败',
          icon: 'error'
        });
      }
    },

    // 切换音频状态
    toggleAudio(forcedState = null) {
      console.log('切换音频状态:', forcedState);

      // 更新音频状态
      if (forcedState !== null) {
        this.isAudioEnabled = forcedState;
      } else {
        this.isAudioEnabled = !this.isAudioEnabled;
      }

      // 应用到媒体流
      if (this.stream) {
        this.stream.getAudioTracks().forEach(track => {
          track.enabled = this.isAudioEnabled;
        });
      }
    },

    // 切换视频状态
    toggleVideo(forcedState = null) {
      console.log('切换视频状态:', forcedState);

      // 更新视频状态
      if (forcedState !== null) {
        this.isVideoEnabled = forcedState;
      } else {
        this.isVideoEnabled = !this.isVideoEnabled;
      }

      // 应用到媒体流
      if (this.stream) {
        this.stream.getVideoTracks().forEach(track => {
          track.enabled = this.isVideoEnabled;
        });
      }
    },

    // 切换摄像头（前置/后置）
    async switchCamera() {
      console.log('切换摄像头');
      try {
        // 如果已有流，关闭现有轨道
        if (this.stream) {
          this.stream.getTracks().forEach(track => track.stop());
        }
        if (this.originalStream) {
          this.originalStream.getTracks().forEach(track => track.stop());
        }

        // 切换摄像头朝向
        this.currentFacingMode = this.currentFacingMode === 'user' ? 'environment' : 'user';

        // 获取新的原始流
        this.originalStream = await getVideoSrcObjectStream(this.currentFacingMode);

        // 根据美颜设置决定使用哪个流
        if (this.beautyEnabled && this.beautyFilter) {
          try {
            this.stream = await getBeautyVideoSrcObjectStream(this.currentFacingMode, 720, this.beautyFilter);
          } catch (error) {
            console.error('美颜流创建失败，使用原始流:', error);
            this.stream = this.originalStream;
          }
        } else {
          this.stream = this.originalStream;
        }

        // 保持音频视频状态
        this.stream.getAudioTracks().forEach(track => {
          track.enabled = this.isAudioEnabled;
        });

        this.stream.getVideoTracks().forEach(track => {
          track.enabled = this.isVideoEnabled;
        });

        // 更新视频源
        if (this.videoElement) {
          this.videoElement.srcObject = this.stream;
        }

        // 如果已在推流中，需要替换发布者的轨道
        if (this.isLiving && this.publisher && this.publisher.pc) {
          const senders = this.publisher.pc.getSenders();
          const videoTrack = this.stream.getVideoTracks()[0];

          // 找到视频发送器并替换轨道
          const videoSender = senders.find(sender => sender.track && sender.track.kind === 'video');
          if (videoSender) {
            videoSender.replaceTrack(videoTrack);
          }
        }
      } catch (error) {
        console.error('切换摄像头失败:', error);
        uni.showToast({
          title: '切换摄像头失败',
          icon: 'error'
        });
      }
    },

    // 处理美颜设置变化
    async handleBeautySettingsChange(data) {
      console.log('美颜设置变化:', data);

      this.beautyEnabled = data.enabled;
      this.beautySettings = { ...data.settings };

      if (this.beautyFilter) {
        this.beautyFilter.setEnabled(this.beautyEnabled);
        this.beautyFilter.updateSettings(this.beautySettings);

        // 如果有原始流，重新应用美颜效果
        if (this.originalStream) {
          try {
            const newStream = await updateBeautyStream(this.videoElement, this.originalStream, this.beautyFilter);
            this.stream = newStream;

            // 如果正在推流，需要替换轨道
            if (this.isLiving && this.publisher && this.publisher.pc) {
              const senders = this.publisher.pc.getSenders();
              const videoTrack = this.stream.getVideoTracks()[0];

              const videoSender = senders.find(sender => sender.track && sender.track.kind === 'video');
              if (videoSender) {
                videoSender.replaceTrack(videoTrack);
              }
            }
          } catch (error) {
            console.error('更新美颜效果失败:', error);
          }
        }
      }
    },


    // 开始直播
    async startLive() {
      if (!this.roomId || !this.display) {
        uni.showToast({
          title: this.$t('房间未初始化'),
          icon: 'error'
        });
        return;
      }
      if (this.isLiving) return;

      try {
        console.log('开始直播');

        // 确保已有媒体流
        if (!this.stream) {
          await this.startCameraPreview();
        }

        // 创建SRS RTC发布者
        this.publisher = new SrsRtcPublisherAsync();

        const displayUserId = User.getUserId();
        // 生成WebRTC URL
        const url = 'webrtc://' + Config.LIVE_SIG_HOST + '/r' + String(this.roomId) + '/d' + String(displayUserId);
        console.log('WebRTC URL:', url);

        // 使用已存在的流开始推流
        const session = await this.publisher.publishWithExistingStream(url, this.stream);
        console.log('推流成功:', session);

        // 推流成功后更新状态
        this.isLiving = true;

        // 如果是主播通知服务器开播成功
        if (this.isPush) {
          AppsLiveApiLiveRoomsApiStartSocketSend({
            id: this.roomId
          });
        }


        uni.$emit('liveStatusChange', this.isLiving)
        this.videoElement.muted = false;
        uni.showToast({
          title: this.isPush ? this.$t('直播已开始') : this.$t('连麦已开始'),
          icon: 'success'
        });
      } catch (error) {
        console.error('开始直播失败:', error);

        // 清理资源
        if (this.publisher) {
          this.publisher.close();
          this.publisher = null;
        }

        uni.showToast({
          title: this.$t('开始直播失败'),
          icon: 'none',
          success: () => {
            window.location.reload();
          }
        });
      } finally {

      }
    },

    // 停止直播
    stopLive() {
      if (!this.isLiving) {
        window.webUni.switchTab({
          url:'/pages/live/index'
        })
        return;
      }

      uni.showModal({
        title: this.$t('确定结束直播吗？'),
        content: this.$t('结束后将无法恢复'),
        confirmText: this.$t('确定'),
        cancelText: this.$t('取消'),
        success: (res) => {
          if (res.confirm) {
            // 关闭发布者
            if (this.publisher) {
              this.publisher.close();
              this.publisher = null;
            }

            // 更新状态
            this.isLiving = false;

            // 通知服务器下播
            AppsLiveApiLiveRoomsApiOfflineSocketSend({
              id: this.roomId
            });
            uni.$emit('liveStatusChange', this.isLiving)
            this.cleanupResources();
            uni.showToast({
              title: this.$t('直播已结束'),
              icon: 'success'
            });
          }
        }
      });
    },

    // 清理资源
    cleanupResources() {


      // 停止媒体流
      if (this.stream) {
        this.stream.getTracks().forEach(track => track.stop());
        this.stream = null;
      }

      // 关闭发布者
      if (this.publisher) {
        this.publisher.close();
        this.publisher = null;
      }
    },


  },
}
</script>
<style scoped lang="scss">
.publisher {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
  background-color: #000;
  /* 添加黑色背景 */
  overflow: hidden;
  /* 防止内容溢出 */
}

/* 添加自定义样式，覆盖 createVideo 中的样式 */
:deep(#publisher-video) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  /* 改为 cover 而不是 contain */
  position: absolute;
  top: 0;
  left: 0;
}
</style>