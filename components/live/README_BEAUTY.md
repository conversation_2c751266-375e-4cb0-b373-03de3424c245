# 直播美颜功能说明

## 功能概述

本项目为SRS服务器WebRTC直播推流页面增加了完整的美颜功能，支持实时视频美颜处理并推流到服务器。美颜功能采用模块化设计，易于维护和扩展。

## 功能特性

### 🎨 美颜效果
- **磨皮**: 平滑肌肤，减少瑕疵
- **美白**: 提升肌肤亮度和白皙度
- **亮度调节**: 调整画面整体亮度
- **对比度调节**: 增强画面层次感
- **饱和度调节**: 调整色彩饱和度
- **锐化**: 增强画面清晰度

### 🎛️ 预设滤镜
- **自然**: 轻度美颜，保持自然效果
- **甜美**: 强化美白和磨皮，营造甜美感
- **冷酷**: 增强对比度和锐化，营造冷酷感
- **温暖**: 提升饱和度和亮度，营造温暖感

### ⚙️ 技术特性
- **WebGL加速**: 使用GPU加速处理，性能优异
- **Canvas回退**: 不支持WebGL时自动回退到Canvas 2D
- **实时处理**: 30fps实时美颜处理
- **设置持久化**: 美颜设置自动保存到本地存储
- **模块化设计**: 易于维护和扩展

## 文件结构

```
components/live/
├── js/
│   ├── beauty-filter.js      # 美颜滤镜核心模块
│   ├── beauty-test.js        # 美颜功能测试工具
│   └── live.tool.js          # 扩展的直播工具函数
├── BeautySettings.vue        # 美颜设置界面组件
├── publisher.vue             # 更新的推流组件
├── SettingsPopup.vue         # 更新的设置弹窗
└── README_BEAUTY.md          # 本说明文档
```

## 使用方法

### 1. 基本使用

在直播推流页面中，用户可以通过以下步骤使用美颜功能：

1. 点击设置按钮打开直播设置面板
2. 点击"美颜设置"按钮打开美颜设置界面
3. 开启美颜总开关
4. 调节各项美颜参数或选择预设滤镜
5. 设置会自动保存并实时应用到视频流

### 2. 开发集成

#### 引入美颜滤镜
```javascript
import { BeautyFilter } from "@/components/live/js/beauty-filter";

// 创建美颜滤镜实例
const beautyFilter = new BeautyFilter();
```

#### 初始化美颜流
```javascript
import { getBeautyVideoSrcObjectStream } from "@/components/live/js/live.tool";

// 获取带美颜的视频流
const stream = await getBeautyVideoSrcObjectStream('user', 720, beautyFilter);
```

#### 更新美颜设置
```javascript
// 监听美颜设置变化
uni.$on('beautySettingsChange', (data) => {
    beautyFilter.setEnabled(data.enabled);
    beautyFilter.updateSettings(data.settings);
});
```

### 3. 美颜设置组件

```vue
<template>
  <!-- 在页面中引入美颜设置组件 -->
  <beauty-settings ref="beautySettings"></beauty-settings>
</template>

<script>
import BeautySettings from "@/components/live/BeautySettings";

export default {
  components: {
    BeautySettings
  },
  methods: {
    openBeautySettings() {
      this.$refs.beautySettings.open();
    }
  }
}
</script>
```

## API 参考

### BeautyFilter 类

#### 构造函数
```javascript
const beautyFilter = new BeautyFilter();
```

#### 主要方法

##### `initCanvas(width, height)`
初始化Canvas和WebGL上下文
- `width`: Canvas宽度
- `height`: Canvas高度
- 返回: `boolean` - 是否成功初始化WebGL

##### `setEnabled(enabled)`
启用/禁用美颜
- `enabled`: `boolean` - 是否启用美颜

##### `updateSettings(settings)`
更新美颜设置
- `settings`: `Object` - 美颜参数对象

##### `processFrame(video)`
处理视频帧
- `video`: `HTMLVideoElement` - 视频元素
- 返回: `HTMLCanvasElement` - 处理后的Canvas

##### `getSettings()`
获取当前美颜设置
- 返回: `Object` - 当前美颜参数

##### `resetSettings()`
重置美颜设置为默认值

##### `destroy()`
销毁美颜滤镜，释放资源

### 美颜参数说明

```javascript
const settings = {
  smoothing: 0.5,    // 磨皮强度 (0-1)
  whitening: 0.3,    // 美白强度 (0-1)
  brightness: 0.1,   // 亮度调节 (-1到1)
  contrast: 0.1,     // 对比度调节 (-1到1)
  saturation: 0.1,   // 饱和度调节 (-1到1)
  sharpening: 0.2    // 锐化强度 (0-1)
};
```

## 浏览器兼容性

### 支持的浏览器
- Chrome 51+
- Firefox 50+
- Safari 11+
- Edge 79+

### 功能支持
- **WebGL**: 现代浏览器均支持，提供最佳性能
- **Canvas 2D**: 所有浏览器支持，作为回退方案
- **getUserMedia**: 现代浏览器均支持
- **captureStream**: Chrome 51+, Firefox 43+

## 性能优化

### 1. WebGL优化
- 使用GPU加速处理，大幅提升性能
- 优化着色器代码，减少计算复杂度
- 合理使用纹理缓存

### 2. 内存管理
- 及时释放不用的资源
- 避免内存泄漏
- 优化Canvas尺寸

### 3. 帧率控制
- 默认30fps处理，平衡性能和效果
- 可根据设备性能动态调整

## 故障排除

### 常见问题

#### 1. 美颜效果不生效
- 检查浏览器是否支持WebGL
- 确认美颜开关是否开启
- 查看控制台是否有错误信息

#### 2. 性能问题
- 降低视频分辨率
- 减少美颜参数强度
- 检查设备性能

#### 3. 兼容性问题
- 更新浏览器到最新版本
- 检查WebGL支持情况
- 使用Canvas 2D回退模式

### 调试工具

使用内置的测试工具进行功能验证：

```javascript
import { BeautyTest } from "@/components/live/js/beauty-test";

const test = new BeautyTest();
test.runAllTests().then(results => {
  console.log('测试结果:', results);
});
```

## 扩展开发

### 添加新的美颜效果

1. 在`beauty-filter.js`中的片段着色器添加新的效果算法
2. 在`BeautySettings.vue`中添加对应的参数控制
3. 更新设置对象和预设配置

### 自定义预设滤镜

在`BeautySettings.vue`中的`presets`对象中添加新的预设：

```javascript
presets: {
  // 现有预设...
  custom: {
    smoothing: 0.7,
    whitening: 0.4,
    brightness: 0.2,
    contrast: 0.15,
    saturation: 0.25,
    sharpening: 0.1
  }
}
```

## 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- ✨ 支持基础美颜效果（磨皮、美白、亮度、对比度、饱和度、锐化）
- ✨ 提供4种预设滤镜
- ✨ WebGL加速和Canvas 2D回退
- ✨ 设置持久化存储
- ✨ 完整的测试工具

## 技术支持

如有问题或建议，请联系开发团队或提交Issue。
