<template>
  <view>
    <!-- 美颜设置弹窗 -->
    <uni-popup ref="beautyPopup" type="bottom" :safe-area="true">
      <view class="beauty-settings">
        <view class="beauty-header">
          <text class="beauty-title">{{ $t('美颜设置') }}</text>
          <view class="header-controls">
            <button class="reset-btn" @click="resetSettings">{{ $t('重置') }}</button>
            <view class="close-btn" @click="closeBeautyPopup">
              <uni-icons type="close" size="24" color="#ffffff"></uni-icons>
            </view>
          </view>
        </view>

        <view class="beauty-content">
          <!-- 美颜总开关 -->
          <view class="beauty-item">
            <text class="beauty-label">{{ $t('美颜') }}</text>
            <switch :checked="beautyEnabled" @change="handleBeautyToggle" color="#FF3B5C"></switch>
          </view>

          <!-- 美颜参数调节 -->
          <view class="beauty-controls" v-if="beautyEnabled">
            
            <!-- 磨皮 -->
            <view class="beauty-item">
              <view class="label-row">
                <text class="beauty-label">{{ $t('磨皮') }}</text>
                <text class="beauty-value">{{ Math.round(beautySettings.smoothing * 100) }}</text>
              </view>
              <slider 
                class="beauty-slider"
                :value="beautySettings.smoothing * 100"
                @change="handleSliderChange('smoothing', $event)"
                min="0" 
                max="100" 
                step="1"
                activeColor="#FF3B5C"
                backgroundColor="rgba(255,255,255,0.3)"
              />
            </view>

            <!-- 美白 -->
            <view class="beauty-item">
              <view class="label-row">
                <text class="beauty-label">{{ $t('美白') }}</text>
                <text class="beauty-value">{{ Math.round(beautySettings.whitening * 100) }}</text>
              </view>
              <slider 
                class="beauty-slider"
                :value="beautySettings.whitening * 100"
                @change="handleSliderChange('whitening', $event)"
                min="0" 
                max="100" 
                step="1"
                activeColor="#FF3B5C"
                backgroundColor="rgba(255,255,255,0.3)"
              />
            </view>

            <!-- 亮度 -->
            <view class="beauty-item">
              <view class="label-row">
                <text class="beauty-label">{{ $t('亮度') }}</text>
                <text class="beauty-value">{{ Math.round((beautySettings.brightness + 1) * 50) }}</text>
              </view>
              <slider 
                class="beauty-slider"
                :value="(beautySettings.brightness + 1) * 50"
                @change="handleSliderChange('brightness', $event, true)"
                min="0" 
                max="100" 
                step="1"
                activeColor="#FF3B5C"
                backgroundColor="rgba(255,255,255,0.3)"
              />
            </view>

            <!-- 对比度 -->
            <view class="beauty-item">
              <view class="label-row">
                <text class="beauty-label">{{ $t('对比度') }}</text>
                <text class="beauty-value">{{ Math.round((beautySettings.contrast + 1) * 50) }}</text>
              </view>
              <slider 
                class="beauty-slider"
                :value="(beautySettings.contrast + 1) * 50"
                @change="handleSliderChange('contrast', $event, true)"
                min="0" 
                max="100" 
                step="1"
                activeColor="#FF3B5C"
                backgroundColor="rgba(255,255,255,0.3)"
              />
            </view>

            <!-- 饱和度 -->
            <view class="beauty-item">
              <view class="label-row">
                <text class="beauty-label">{{ $t('饱和度') }}</text>
                <text class="beauty-value">{{ Math.round((beautySettings.saturation + 1) * 50) }}</text>
              </view>
              <slider 
                class="beauty-slider"
                :value="(beautySettings.saturation + 1) * 50"
                @change="handleSliderChange('saturation', $event, true)"
                min="0" 
                max="100" 
                step="1"
                activeColor="#FF3B5C"
                backgroundColor="rgba(255,255,255,0.3)"
              />
            </view>

            <!-- 锐化 -->
            <view class="beauty-item">
              <view class="label-row">
                <text class="beauty-label">{{ $t('锐化') }}</text>
                <text class="beauty-value">{{ Math.round(beautySettings.sharpening * 100) }}</text>
              </view>
              <slider 
                class="beauty-slider"
                :value="beautySettings.sharpening * 100"
                @change="handleSliderChange('sharpening', $event)"
                min="0" 
                max="100" 
                step="1"
                activeColor="#FF3B5C"
                backgroundColor="rgba(255,255,255,0.3)"
              />
            </view>

          </view>

          <!-- 预设滤镜 -->
          <view class="beauty-presets" v-if="beautyEnabled">
            <text class="preset-title">{{ $t('预设滤镜') }}</text>
            <view class="preset-buttons">
              <button 
                class="preset-btn" 
                :class="{ active: currentPreset === 'natural' }"
                @click="applyPreset('natural')"
              >
                {{ $t('自然') }}
              </button>
              <button 
                class="preset-btn" 
                :class="{ active: currentPreset === 'sweet' }"
                @click="applyPreset('sweet')"
              >
                {{ $t('甜美') }}
              </button>
              <button 
                class="preset-btn" 
                :class="{ active: currentPreset === 'cool' }"
                @click="applyPreset('cool')"
              >
                {{ $t('冷酷') }}
              </button>
              <button 
                class="preset-btn" 
                :class="{ active: currentPreset === 'warm' }"
                @click="applyPreset('warm')"
              >
                {{ $t('温暖') }}
              </button>
            </view>
          </view>

        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import i18n from "@/Lib/i18n";

export default {
  name: "BeautySettings",
  data() {
    return {
      beautyEnabled: false,
      currentPreset: 'natural',
      beautySettings: {
        smoothing: 0.5,      // 磨皮强度 0-1
        whitening: 0.3,      // 美白强度 0-1
        brightness: 0.1,     // 亮度调节 -1到1
        contrast: 0.1,       // 对比度调节 -1到1
        saturation: 0.1,     // 饱和度调节 -1到1
        sharpening: 0.2      // 锐化强度 0-1
      },
      // 预设滤镜配置
      presets: {
        natural: {
          smoothing: 0.3,
          whitening: 0.2,
          brightness: 0.05,
          contrast: 0.05,
          saturation: 0.05,
          sharpening: 0.1
        },
        sweet: {
          smoothing: 0.6,
          whitening: 0.5,
          brightness: 0.15,
          contrast: -0.1,
          saturation: 0.2,
          sharpening: 0.0
        },
        cool: {
          smoothing: 0.2,
          whitening: 0.1,
          brightness: -0.05,
          contrast: 0.2,
          saturation: -0.1,
          sharpening: 0.3
        },
        warm: {
          smoothing: 0.4,
          whitening: 0.3,
          brightness: 0.1,
          contrast: 0.1,
          saturation: 0.3,
          sharpening: 0.15
        }
      }
    }
  },
  created() {
    uni.$on('openBeautySettings', this.open);
    // 从本地存储加载设置
    this.loadSettings();
  },
  unmounted() {
    uni.$off('openBeautySettings', this.open);
  },
  methods: {
    $t(text) {
      return i18n.t(text);
    },
    
    // 打开美颜设置弹窗
    open() {
      this.$refs.beautyPopup.open();
    },
    
    // 关闭美颜设置弹窗
    closeBeautyPopup() {
      this.$refs.beautyPopup.close();
    },
    
    // 处理美颜开关
    handleBeautyToggle(e) {
      this.beautyEnabled = e.detail.value;
      this.saveSettings();
      this.emitSettingsChange();
    },
    
    // 处理滑块变化
    handleSliderChange(key, event, isBipolar = false) {
      let value = event.detail.value / 100;
      
      // 对于双极性参数（亮度、对比度、饱和度），需要转换为-1到1的范围
      if (isBipolar) {
        value = (value * 2) - 1;
      }
      
      this.beautySettings[key] = value;
      this.currentPreset = 'custom'; // 手动调节后设为自定义
      this.saveSettings();
      this.emitSettingsChange();
    },
    
    // 应用预设滤镜
    applyPreset(presetName) {
      if (this.presets[presetName]) {
        this.beautySettings = { ...this.presets[presetName] };
        this.currentPreset = presetName;
        this.saveSettings();
        this.emitSettingsChange();
        
        uni.showToast({
          title: this.$t('已应用') + this.$t(presetName) + this.$t('滤镜'),
          icon: 'success',
          duration: 1500
        });
      }
    },
    
    // 重置设置
    resetSettings() {
      this.beautySettings = {
        smoothing: 0.5,
        whitening: 0.3,
        brightness: 0.1,
        contrast: 0.1,
        saturation: 0.1,
        sharpening: 0.2
      };
      this.currentPreset = 'natural';
      this.saveSettings();
      this.emitSettingsChange();
      
      uni.showToast({
        title: this.$t('已重置美颜设置'),
        icon: 'success'
      });
    },
    
    // 发送设置变化事件
    emitSettingsChange() {
      uni.$emit('beautySettingsChange', {
        enabled: this.beautyEnabled,
        settings: { ...this.beautySettings }
      });
    },
    
    // 保存设置到本地存储
    saveSettings() {
      try {
        const settingsData = {
          enabled: this.beautyEnabled,
          settings: this.beautySettings,
          preset: this.currentPreset
        };
        uni.setStorageSync('beautySettings', JSON.stringify(settingsData));
      } catch (error) {
        console.error('保存美颜设置失败:', error);
      }
    },
    
    // 从本地存储加载设置
    loadSettings() {
      try {
        const savedSettings = uni.getStorageSync('beautySettings');
        if (savedSettings) {
          const settingsData = JSON.parse(savedSettings);
          this.beautyEnabled = settingsData.enabled || false;
          this.beautySettings = { ...this.beautySettings, ...settingsData.settings };
          this.currentPreset = settingsData.preset || 'natural';
        }
      } catch (error) {
        console.error('加载美颜设置失败:', error);
      }
    },
    
    // 获取当前设置
    getCurrentSettings() {
      return {
        enabled: this.beautyEnabled,
        settings: { ...this.beautySettings }
      };
    }
  }
}
</script>

<style scoped lang="scss">
.beauty-settings {
  background-color: rgba(0, 0, 0, 0.9);
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
  max-height: 80vh;
  overflow-y: auto;

  .beauty-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);

    .beauty-title {
      font-size: 18px;
      font-weight: bold;
      color: #ffffff;
    }

    .header-controls {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .reset-btn {
        background-color: rgba(255, 59, 92, 0.2);
        color: #FF3B5C;
        border: 1rpx solid #FF3B5C;
        border-radius: 30rpx;
        padding: 10rpx 20rpx;
        font-size: 14px;
      }

      .close-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .beauty-content {
    .beauty-item {
      margin-bottom: 30rpx;

      .label-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15rpx;
      }

      .beauty-label {
        font-size: 16px;
        color: #ffffff;
      }

      .beauty-value {
        font-size: 14px;
        color: #FF3B5C;
        font-weight: bold;
      }

      .beauty-slider {
        width: 100%;
      }
    }

    .beauty-controls {
      margin-top: 20rpx;
    }

    .beauty-presets {
      margin-top: 40rpx;
      padding-top: 30rpx;
      border-top: 1rpx solid rgba(255, 255, 255, 0.1);

      .preset-title {
        font-size: 16px;
        color: #ffffff;
        margin-bottom: 20rpx;
        display: block;
      }

      .preset-buttons {
        display: flex;
        gap: 15rpx;
        flex-wrap: wrap;

        .preset-btn {
          flex: 1;
          min-width: 120rpx;
          background-color: rgba(255, 255, 255, 0.1);
          color: #ffffff;
          border: 1rpx solid rgba(255, 255, 255, 0.2);
          border-radius: 25rpx;
          padding: 15rpx 20rpx;
          font-size: 14px;
          text-align: center;

          &.active {
            background-color: #FF3B5C;
            border-color: #FF3B5C;
            color: #ffffff;
          }
        }
      }
    }
  }
}
</style>
