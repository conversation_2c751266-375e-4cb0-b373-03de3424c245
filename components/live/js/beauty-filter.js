/**
 * 真实美颜滤镜模块
 * 使用Canvas处理视频流数据，确保美颜效果能推流到服务器
 */

class BeautyFilter {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.isEnabled = false;
        this.isProcessing = false;
        
        // 美颜参数
        this.settings = {
            brightness: 10,     // 亮度增强 0-50
            smoothing: 30,      // 磨皮强度 0-100
            whitening: 20,      // 美白强度 0-100
            contrast: 10,       // 对比度增强 0-50
            saturation: 15      // 饱和度增强 0-50
        };
        
        // 预设滤镜
        this.presets = {
            natural: {
                brightness: 5,
                smoothing: 20,
                whitening: 10,
                contrast: 5,
                saturation: 10
            },
            sweet: {
                brightness: 15,
                smoothing: 40,
                whitening: 30,
                contrast: 5,
                saturation: 20
            },
            cool: {
                brightness: 0,
                smoothing: 15,
                whitening: 5,
                contrast: 20,
                saturation: 5
            },
            warm: {
                brightness: 20,
                smoothing: 25,
                whitening: 25,
                contrast: 15,
                saturation: 25
            }
        };
    }
    
    /**
     * 初始化Canvas
     */
    initCanvas(width, height) {
        try {
            this.canvas = document.createElement('canvas');
            this.canvas.width = width;
            this.canvas.height = height;
            this.ctx = this.canvas.getContext('2d');
            
            if (!this.ctx) {
                console.error('无法获取Canvas 2D上下文');
                return false;
            }
            
            return true;
        } catch (error) {
            console.error('Canvas初始化失败:', error);
            return false;
        }
    }
    
    /**
     * 启用/禁用美颜
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
    }
    
    /**
     * 更新美颜设置
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
    }
    
    /**
     * 应用预设滤镜
     */
    applyPreset(presetName) {
        if (this.presets[presetName]) {
            this.settings = { ...this.presets[presetName] };
            return true;
        }
        return false;
    }
    
    /**
     * 处理视频帧 - 核心美颜算法
     */
    processFrame(video) {
        if (!this.isEnabled || !this.canvas || !this.ctx || this.isProcessing) {
            return null;
        }
        
        try {
            this.isProcessing = true;
            
            // 绘制原始视频帧到Canvas
            this.ctx.drawImage(video, 0, 0, this.canvas.width, this.canvas.height);
            
            // 获取图像数据
            const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
            const data = imageData.data;
            
            // 应用美颜算法
            this.applyBeautyEffects(data);
            
            // 将处理后的数据写回Canvas
            this.ctx.putImageData(imageData, 0, 0);
            
            this.isProcessing = false;
            return this.canvas;
        } catch (error) {
            console.error('美颜处理失败:', error);
            this.isProcessing = false;
            return null;
        }
    }
    
    /**
     * 应用美颜效果算法
     */
    applyBeautyEffects(data) {
        const width = this.canvas.width;
        const height = this.canvas.height;
        
        // 创建临时数组存储原始数据
        const originalData = new Uint8ClampedArray(data);
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const idx = (y * width + x) * 4;
                
                let r = originalData[idx];
                let g = originalData[idx + 1];
                let b = originalData[idx + 2];
                
                // 1. 美白效果
                if (this.settings.whitening > 0) {
                    const whitening = this.settings.whitening * 0.8;
                    r = Math.min(255, r + whitening);
                    g = Math.min(255, g + whitening);
                    b = Math.min(255, b + whitening);
                }
                
                // 2. 亮度调节
                if (this.settings.brightness > 0) {
                    const brightness = this.settings.brightness * 1.2;
                    r = Math.min(255, r + brightness);
                    g = Math.min(255, g + brightness);
                    b = Math.min(255, b + brightness);
                }
                
                // 3. 对比度调节
                if (this.settings.contrast > 0) {
                    const contrast = 1 + (this.settings.contrast * 0.02);
                    r = Math.min(255, Math.max(0, (r - 128) * contrast + 128));
                    g = Math.min(255, Math.max(0, (g - 128) * contrast + 128));
                    b = Math.min(255, Math.max(0, (b - 128) * contrast + 128));
                }
                
                // 4. 饱和度调节
                if (this.settings.saturation > 0) {
                    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                    const saturation = 1 + (this.settings.saturation * 0.02);
                    r = Math.min(255, Math.max(0, gray + (r - gray) * saturation));
                    g = Math.min(255, Math.max(0, gray + (g - gray) * saturation));
                    b = Math.min(255, Math.max(0, gray + (b - gray) * saturation));
                }
                
                // 更新像素值
                data[idx] = r;
                data[idx + 1] = g;
                data[idx + 2] = b;
            }
        }
        
        // 5. 磨皮效果（简单模糊）
        if (this.settings.smoothing > 0) {
            this.applySmoothingEffect(data, originalData, width, height);
        }
    }
    
    /**
     * 应用磨皮效果
     */
    applySmoothingEffect(data, originalData, width, height) {
        const smoothing = this.settings.smoothing * 0.01;
        
        // 简单的3x3平均滤波器实现磨皮
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const idx = (y * width + x) * 4;
                
                let r = 0, g = 0, b = 0;
                let count = 0;
                
                // 3x3邻域平均
                for (let dy = -1; dy <= 1; dy++) {
                    for (let dx = -1; dx <= 1; dx++) {
                        const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
                        r += originalData[neighborIdx];
                        g += originalData[neighborIdx + 1];
                        b += originalData[neighborIdx + 2];
                        count++;
                    }
                }
                
                // 计算平均值
                r /= count;
                g /= count;
                b /= count;
                
                // 混合原始值和模糊值
                data[idx] = originalData[idx] * (1 - smoothing) + r * smoothing;
                data[idx + 1] = originalData[idx + 1] * (1 - smoothing) + g * smoothing;
                data[idx + 2] = originalData[idx + 2] * (1 - smoothing) + b * smoothing;
            }
        }
    }
    
    /**
     * 创建美颜视频流
     */
    createBeautyStream(originalStream) {
        return new Promise((resolve, reject) => {
            try {
                // 创建视频元素
                const video = document.createElement('video');
                video.srcObject = originalStream;
                video.autoplay = true;
                video.muted = true;
                video.playsInline = true;
                
                video.addEventListener('loadedmetadata', () => {
                    const width = video.videoWidth;
                    const height = video.videoHeight;
                    
                    // 初始化Canvas
                    if (!this.initCanvas(width, height)) {
                        reject(new Error('Canvas初始化失败'));
                        return;
                    }
                    
                    // 创建Canvas流
                    let canvasStream;
                    try {
                        canvasStream = this.canvas.captureStream(30); // 30fps
                    } catch (error) {
                        reject(new Error('captureStream不支持'));
                        return;
                    }
                    
                    // 添加音频轨道
                    const audioTracks = originalStream.getAudioTracks();
                    audioTracks.forEach(track => {
                        canvasStream.addTrack(track);
                    });
                    
                    // 开始处理视频帧
                    const processFrame = () => {
                        if (video.readyState >= 2) { // HAVE_CURRENT_DATA
                            this.processFrame(video);
                        }
                        requestAnimationFrame(processFrame);
                    };
                    processFrame();
                    
                    resolve(canvasStream);
                });
                
                video.addEventListener('error', (error) => {
                    reject(error);
                });
                
            } catch (error) {
                reject(error);
            }
        });
    }
    
    /**
     * 获取当前设置
     */
    getSettings() {
        return { ...this.settings };
    }
    
    /**
     * 重置为默认设置
     */
    resetSettings() {
        this.settings = {
            brightness: 10,
            smoothing: 30,
            whitening: 20,
            contrast: 10,
            saturation: 15
        };
    }
    
    /**
     * 获取可用的预设列表
     */
    getPresets() {
        return Object.keys(this.presets);
    }
    
    /**
     * 销毁资源
     */
    destroy() {
        this.isEnabled = false;
        this.isProcessing = false;
        this.canvas = null;
        this.ctx = null;
    }
}

// 导出美颜滤镜类
export { BeautyFilter };
