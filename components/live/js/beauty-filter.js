/**
 * 实用美颜滤镜模块
 * 使用CSS滤镜实现，简单高效，兼容性好
 */

class BeautyFilter {
    constructor() {
        this.isEnabled = false;
        this.settings = {
            brightness: 1.1,    // 亮度 0.5-2.0
            contrast: 1.1,      // 对比度 0.5-2.0  
            saturate: 1.2,      // 饱和度 0-3.0
            blur: 0,            // 磨皮模糊 0-5px
            sepia: 0,           // 复古效果 0-1
            hueRotate: 0        // 色调旋转 0-360deg
        };
        
        // 预设滤镜
        this.presets = {
            natural: {
                brightness: 1.05,
                contrast: 1.05,
                saturate: 1.1,
                blur: 0.5,
                sepia: 0,
                hueRotate: 0
            },
            sweet: {
                brightness: 1.15,
                contrast: 0.95,
                saturate: 1.3,
                blur: 1,
                sepia: 0.1,
                hueRotate: 10
            },
            cool: {
                brightness: 0.95,
                contrast: 1.2,
                saturate: 0.9,
                blur: 0,
                sepia: 0,
                hueRotate: 200
            },
            warm: {
                brightness: 1.1,
                contrast: 1.1,
                saturate: 1.4,
                blur: 0.5,
                sepia: 0.2,
                hueRotate: 30
            }
        };
    }
    
    /**
     * 启用/禁用美颜
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
    }
    
    /**
     * 更新美颜设置
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
    }
    
    /**
     * 应用预设滤镜
     */
    applyPreset(presetName) {
        if (this.presets[presetName]) {
            this.settings = { ...this.presets[presetName] };
            return true;
        }
        return false;
    }
    
    /**
     * 获取CSS滤镜字符串
     */
    getCSSFilter() {
        if (!this.isEnabled) {
            return 'none';
        }
        
        const filters = [];
        
        if (this.settings.brightness !== 1) {
            filters.push(`brightness(${this.settings.brightness})`);
        }
        
        if (this.settings.contrast !== 1) {
            filters.push(`contrast(${this.settings.contrast})`);
        }
        
        if (this.settings.saturate !== 1) {
            filters.push(`saturate(${this.settings.saturate})`);
        }
        
        if (this.settings.blur > 0) {
            filters.push(`blur(${this.settings.blur}px)`);
        }
        
        if (this.settings.sepia > 0) {
            filters.push(`sepia(${this.settings.sepia})`);
        }
        
        if (this.settings.hueRotate !== 0) {
            filters.push(`hue-rotate(${this.settings.hueRotate}deg)`);
        }
        
        return filters.length > 0 ? filters.join(' ') : 'none';
    }
    
    /**
     * 应用美颜效果到视频元素
     */
    applyToVideo(videoElement) {
        if (!videoElement) return;
        
        const filter = this.getCSSFilter();
        videoElement.style.filter = filter;
        videoElement.style.webkitFilter = filter; // Safari兼容
    }
    
    /**
     * 移除美颜效果
     */
    removeFromVideo(videoElement) {
        if (!videoElement) return;
        
        videoElement.style.filter = 'none';
        videoElement.style.webkitFilter = 'none';
    }
    
    /**
     * 获取当前设置
     */
    getSettings() {
        return { ...this.settings };
    }
    
    /**
     * 重置为默认设置
     */
    resetSettings() {
        this.settings = {
            brightness: 1.1,
            contrast: 1.1,
            saturate: 1.2,
            blur: 0,
            sepia: 0,
            hueRotate: 0
        };
    }
    
    /**
     * 获取可用的预设列表
     */
    getPresets() {
        return Object.keys(this.presets);
    }
    
    /**
     * 销毁资源
     */
    destroy() {
        // CSS滤镜不需要特殊清理
    }
}

// 导出美颜滤镜类
export { BeautyFilter };
