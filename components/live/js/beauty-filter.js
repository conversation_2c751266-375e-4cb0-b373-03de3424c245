/**
 * 美颜滤镜处理模块
 * 提供实时视频美颜功能
 */

class BeautyFilter {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.gl = null;
        this.program = null;
        this.isEnabled = false;
        
        // 美颜参数
        this.settings = {
            smoothing: 0.5,      // 磨皮强度 0-1
            whitening: 0.3,      // 美白强度 0-1
            brightness: 0.1,     // 亮度调节 -1到1
            contrast: 0.1,       // 对比度调节 -1到1
            saturation: 0.1,     // 饱和度调节 -1到1
            sharpening: 0.2      // 锐化强度 0-1
        };
        
        this.initShaders();
    }
    
    /**
     * 初始化WebGL着色器
     */
    initShaders() {
        // 顶点着色器
        this.vertexShaderSource = `
            attribute vec2 a_position;
            attribute vec2 a_texCoord;
            varying vec2 v_texCoord;
            
            void main() {
                gl_Position = vec4(a_position, 0.0, 1.0);
                v_texCoord = a_texCoord;
            }
        `;
        
        // 片段着色器 - 美颜效果
        this.fragmentShaderSource = `
            precision mediump float;
            uniform sampler2D u_texture;
            uniform float u_smoothing;
            uniform float u_whitening;
            uniform float u_brightness;
            uniform float u_contrast;
            uniform float u_saturation;
            uniform float u_sharpening;
            uniform vec2 u_resolution;
            varying vec2 v_texCoord;
            
            // 高斯模糊函数
            vec4 gaussianBlur(sampler2D texture, vec2 coord, vec2 resolution, float radius) {
                vec4 color = vec4(0.0);
                float total = 0.0;
                
                for (float x = -radius; x <= radius; x++) {
                    for (float y = -radius; y <= radius; y++) {
                        vec2 offset = vec2(x, y) / resolution;
                        float weight = exp(-(x*x + y*y) / (2.0 * radius * radius));
                        color += texture2D(texture, coord + offset) * weight;
                        total += weight;
                    }
                }
                
                return color / total;
            }
            
            // 锐化函数
            vec4 sharpen(sampler2D texture, vec2 coord, vec2 resolution, float amount) {
                vec2 step = 1.0 / resolution;
                vec4 center = texture2D(texture, coord);
                vec4 blur = (
                    texture2D(texture, coord + vec2(-step.x, -step.y)) +
                    texture2D(texture, coord + vec2(0.0, -step.y)) +
                    texture2D(texture, coord + vec2(step.x, -step.y)) +
                    texture2D(texture, coord + vec2(-step.x, 0.0)) +
                    texture2D(texture, coord + vec2(step.x, 0.0)) +
                    texture2D(texture, coord + vec2(-step.x, step.y)) +
                    texture2D(texture, coord + vec2(0.0, step.y)) +
                    texture2D(texture, coord + vec2(step.x, step.y))
                ) / 8.0;
                
                return center + (center - blur) * amount;
            }
            
            void main() {
                vec4 originalColor = texture2D(u_texture, v_texCoord);
                vec4 color = originalColor;
                
                // 磨皮效果 - 高斯模糊
                if (u_smoothing > 0.0) {
                    vec4 blurred = gaussianBlur(u_texture, v_texCoord, u_resolution, u_smoothing * 3.0);
                    color = mix(color, blurred, u_smoothing * 0.8);
                }
                
                // 美白效果
                if (u_whitening > 0.0) {
                    color.rgb = mix(color.rgb, vec3(1.0), u_whitening * 0.3);
                }
                
                // 亮度调节
                color.rgb += u_brightness * 0.5;
                
                // 对比度调节
                color.rgb = (color.rgb - 0.5) * (1.0 + u_contrast) + 0.5;
                
                // 饱和度调节
                float gray = dot(color.rgb, vec3(0.299, 0.587, 0.114));
                color.rgb = mix(vec3(gray), color.rgb, 1.0 + u_saturation);
                
                // 锐化效果
                if (u_sharpening > 0.0) {
                    color = sharpen(u_texture, v_texCoord, u_resolution, u_sharpening);
                }
                
                // 确保颜色值在有效范围内
                color.rgb = clamp(color.rgb, 0.0, 1.0);
                
                gl_FragColor = color;
            }
        `;
    }
    
    /**
     * 初始化Canvas和WebGL上下文
     */
    initCanvas(width, height) {
        this.canvas = document.createElement('canvas');
        this.canvas.width = width;
        this.canvas.height = height;
        
        // 尝试获取WebGL上下文
        this.gl = this.canvas.getContext('webgl') || this.canvas.getContext('experimental-webgl');
        
        if (!this.gl) {
            console.warn('WebGL不支持，回退到Canvas 2D');
            this.ctx = this.canvas.getContext('2d');
            return false;
        }
        
        this.setupWebGL();
        return true;
    }
    
    /**
     * 设置WebGL程序
     */
    setupWebGL() {
        const gl = this.gl;
        
        // 创建着色器
        const vertexShader = this.createShader(gl.VERTEX_SHADER, this.vertexShaderSource);
        const fragmentShader = this.createShader(gl.FRAGMENT_SHADER, this.fragmentShaderSource);
        
        // 创建程序
        this.program = gl.createProgram();
        gl.attachShader(this.program, vertexShader);
        gl.attachShader(this.program, fragmentShader);
        gl.linkProgram(this.program);
        
        if (!gl.getProgramParameter(this.program, gl.LINK_STATUS)) {
            console.error('WebGL程序链接失败:', gl.getProgramInfoLog(this.program));
            return;
        }
        
        // 设置顶点数据
        this.setupVertexData();
        
        // 创建纹理
        this.texture = gl.createTexture();
    }
    
    /**
     * 创建着色器
     */
    createShader(type, source) {
        const gl = this.gl;
        const shader = gl.createShader(type);
        gl.shaderSource(shader, source);
        gl.compileShader(shader);
        
        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
            console.error('着色器编译失败:', gl.getShaderInfoLog(shader));
            gl.deleteShader(shader);
            return null;
        }
        
        return shader;
    }
    
    /**
     * 设置顶点数据
     */
    setupVertexData() {
        const gl = this.gl;
        
        // 顶点位置和纹理坐标
        const vertices = new Float32Array([
            -1, -1, 0, 1,
             1, -1, 1, 1,
            -1,  1, 0, 0,
             1,  1, 1, 0
        ]);
        
        const buffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
        gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);
        
        const positionLocation = gl.getAttribLocation(this.program, 'a_position');
        const texCoordLocation = gl.getAttribLocation(this.program, 'a_texCoord');
        
        gl.enableVertexAttribArray(positionLocation);
        gl.enableVertexAttribArray(texCoordLocation);
        
        gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 16, 0);
        gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 16, 8);
    }
    
    /**
     * 处理视频帧
     */
    processFrame(video) {
        if (!this.isEnabled || !this.canvas) {
            return video;
        }
        
        if (this.gl) {
            return this.processFrameWebGL(video);
        } else {
            return this.processFrameCanvas2D(video);
        }
    }
    
    /**
     * 使用WebGL处理视频帧
     */
    processFrameWebGL(video) {
        const gl = this.gl;
        
        // 设置视口
        gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        
        // 使用程序
        gl.useProgram(this.program);
        
        // 更新纹理
        gl.bindTexture(gl.TEXTURE_2D, this.texture);
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, video);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
        
        // 设置uniform变量
        gl.uniform1f(gl.getUniformLocation(this.program, 'u_smoothing'), this.settings.smoothing);
        gl.uniform1f(gl.getUniformLocation(this.program, 'u_whitening'), this.settings.whitening);
        gl.uniform1f(gl.getUniformLocation(this.program, 'u_brightness'), this.settings.brightness);
        gl.uniform1f(gl.getUniformLocation(this.program, 'u_contrast'), this.settings.contrast);
        gl.uniform1f(gl.getUniformLocation(this.program, 'u_saturation'), this.settings.saturation);
        gl.uniform1f(gl.getUniformLocation(this.program, 'u_sharpening'), this.settings.sharpening);
        gl.uniform2f(gl.getUniformLocation(this.program, 'u_resolution'), this.canvas.width, this.canvas.height);
        
        // 绘制
        gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
        
        return this.canvas;
    }
    
    /**
     * 使用Canvas 2D处理视频帧（回退方案）
     */
    processFrameCanvas2D(video) {
        const ctx = this.ctx;
        
        // 绘制原始视频帧
        ctx.drawImage(video, 0, 0, this.canvas.width, this.canvas.height);
        
        // 获取图像数据
        const imageData = ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        const data = imageData.data;
        
        // 简单的美颜处理
        for (let i = 0; i < data.length; i += 4) {
            // 美白效果
            if (this.settings.whitening > 0) {
                data[i] = Math.min(255, data[i] + this.settings.whitening * 30);     // R
                data[i + 1] = Math.min(255, data[i + 1] + this.settings.whitening * 30); // G
                data[i + 2] = Math.min(255, data[i + 2] + this.settings.whitening * 30); // B
            }
            
            // 亮度调节
            if (this.settings.brightness !== 0) {
                const brightness = this.settings.brightness * 50;
                data[i] = Math.max(0, Math.min(255, data[i] + brightness));
                data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + brightness));
                data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + brightness));
            }
        }
        
        // 应用处理后的图像数据
        ctx.putImageData(imageData, 0, 0);
        
        return this.canvas;
    }
    
    /**
     * 更新美颜设置
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
    }
    
    /**
     * 启用/禁用美颜
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
    }
    
    /**
     * 获取当前设置
     */
    getSettings() {
        return { ...this.settings };
    }
    
    /**
     * 重置设置为默认值
     */
    resetSettings() {
        this.settings = {
            smoothing: 0.5,
            whitening: 0.3,
            brightness: 0.1,
            contrast: 0.1,
            saturation: 0.1,
            sharpening: 0.2
        };
    }
    
    /**
     * 销毁资源
     */
    destroy() {
        if (this.gl) {
            this.gl.deleteProgram(this.program);
            this.gl.deleteTexture(this.texture);
        }
        this.canvas = null;
        this.ctx = null;
        this.gl = null;
    }
}

// 导出美颜滤镜类
export { BeautyFilter };
