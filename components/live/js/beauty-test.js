/**
 * 美颜功能测试工具
 * 用于测试美颜滤镜的各项功能
 */

import { BeautyFilter } from './beauty-filter.js';

class BeautyTest {
    constructor() {
        this.beautyFilter = new BeautyFilter();
        this.testResults = [];
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('开始美颜功能测试...');
        
        this.testResults = [];
        
        // 测试美颜滤镜初始化
        await this.testBeautyFilterInit();
        
        // 测试设置更新
        await this.testSettingsUpdate();
        
        // 测试WebGL支持
        await this.testWebGLSupport();
        
        // 测试Canvas回退
        await this.testCanvasFallback();
        
        // 输出测试结果
        this.outputTestResults();
        
        return this.testResults;
    }

    /**
     * 测试美颜滤镜初始化
     */
    async testBeautyFilterInit() {
        try {
            const result = {
                name: '美颜滤镜初始化测试',
                passed: false,
                message: ''
            };

            // 检查美颜滤镜是否正确初始化
            if (this.beautyFilter) {
                result.passed = true;
                result.message = '美颜滤镜初始化成功';
            } else {
                result.message = '美颜滤镜初始化失败';
            }

            this.testResults.push(result);
        } catch (error) {
            this.testResults.push({
                name: '美颜滤镜初始化测试',
                passed: false,
                message: `初始化异常: ${error.message}`
            });
        }
    }

    /**
     * 测试设置更新
     */
    async testSettingsUpdate() {
        try {
            const result = {
                name: '设置更新测试',
                passed: false,
                message: ''
            };

            const testSettings = {
                smoothing: 0.8,
                whitening: 0.6,
                brightness: 0.2,
                contrast: 0.15,
                saturation: 0.1,
                sharpening: 0.3
            };

            // 更新设置
            this.beautyFilter.updateSettings(testSettings);
            
            // 获取当前设置
            const currentSettings = this.beautyFilter.getSettings();
            
            // 验证设置是否正确更新
            let settingsMatch = true;
            for (const key in testSettings) {
                if (Math.abs(currentSettings[key] - testSettings[key]) > 0.001) {
                    settingsMatch = false;
                    break;
                }
            }

            if (settingsMatch) {
                result.passed = true;
                result.message = '设置更新成功';
            } else {
                result.message = '设置更新失败，值不匹配';
            }

            this.testResults.push(result);
        } catch (error) {
            this.testResults.push({
                name: '设置更新测试',
                passed: false,
                message: `设置更新异常: ${error.message}`
            });
        }
    }

    /**
     * 测试WebGL支持
     */
    async testWebGLSupport() {
        try {
            const result = {
                name: 'WebGL支持测试',
                passed: false,
                message: ''
            };

            // 创建测试Canvas
            const testCanvas = document.createElement('canvas');
            testCanvas.width = 640;
            testCanvas.height = 480;

            // 尝试初始化WebGL
            const webglSupported = this.beautyFilter.initCanvas(640, 480);

            if (webglSupported && this.beautyFilter.gl) {
                result.passed = true;
                result.message = 'WebGL支持正常';
            } else {
                result.message = 'WebGL不支持，将使用Canvas 2D回退';
            }

            this.testResults.push(result);
        } catch (error) {
            this.testResults.push({
                name: 'WebGL支持测试',
                passed: false,
                message: `WebGL测试异常: ${error.message}`
            });
        }
    }

    /**
     * 测试Canvas回退
     */
    async testCanvasFallback() {
        try {
            const result = {
                name: 'Canvas 2D回退测试',
                passed: false,
                message: ''
            };

            // 强制使用Canvas 2D
            const originalGl = this.beautyFilter.gl;
            this.beautyFilter.gl = null;
            
            // 重新初始化
            this.beautyFilter.initCanvas(640, 480);

            if (this.beautyFilter.ctx) {
                result.passed = true;
                result.message = 'Canvas 2D回退正常';
            } else {
                result.message = 'Canvas 2D回退失败';
            }

            // 恢复原始状态
            this.beautyFilter.gl = originalGl;

            this.testResults.push(result);
        } catch (error) {
            this.testResults.push({
                name: 'Canvas 2D回退测试',
                passed: false,
                message: `Canvas 2D回退异常: ${error.message}`
            });
        }
    }

    /**
     * 测试美颜效果处理
     */
    async testBeautyProcessing() {
        try {
            const result = {
                name: '美颜效果处理测试',
                passed: false,
                message: ''
            };

            // 创建测试视频元素
            const testVideo = document.createElement('video');
            testVideo.width = 640;
            testVideo.height = 480;

            // 启用美颜
            this.beautyFilter.setEnabled(true);

            // 尝试处理帧
            const processedFrame = this.beautyFilter.processFrame(testVideo);

            if (processedFrame) {
                result.passed = true;
                result.message = '美颜效果处理正常';
            } else {
                result.message = '美颜效果处理失败';
            }

            this.testResults.push(result);
        } catch (error) {
            this.testResults.push({
                name: '美颜效果处理测试',
                passed: false,
                message: `美颜处理异常: ${error.message}`
            });
        }
    }

    /**
     * 输出测试结果
     */
    outputTestResults() {
        console.log('\n=== 美颜功能测试结果 ===');
        
        let passedCount = 0;
        let totalCount = this.testResults.length;

        this.testResults.forEach((result, index) => {
            const status = result.passed ? '✅ 通过' : '❌ 失败';
            console.log(`${index + 1}. ${result.name}: ${status}`);
            console.log(`   ${result.message}`);
            
            if (result.passed) {
                passedCount++;
            }
        });

        console.log(`\n测试总结: ${passedCount}/${totalCount} 项测试通过`);
        
        if (passedCount === totalCount) {
            console.log('🎉 所有测试通过！美颜功能正常。');
        } else {
            console.log('⚠️  部分测试失败，请检查相关功能。');
        }
    }

    /**
     * 获取浏览器兼容性信息
     */
    getBrowserCompatibility() {
        const compatibility = {
            webgl: false,
            canvas2d: false,
            mediaDevices: false,
            captureStream: false
        };

        // 检查WebGL支持
        const testCanvas = document.createElement('canvas');
        const gl = testCanvas.getContext('webgl') || testCanvas.getContext('experimental-webgl');
        compatibility.webgl = !!gl;

        // 检查Canvas 2D支持
        compatibility.canvas2d = !!testCanvas.getContext('2d');

        // 检查MediaDevices API支持
        compatibility.mediaDevices = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);

        // 检查Canvas captureStream支持
        compatibility.captureStream = !!(testCanvas.captureStream || testCanvas.mozCaptureStream);

        return compatibility;
    }

    /**
     * 清理测试资源
     */
    cleanup() {
        if (this.beautyFilter) {
            this.beautyFilter.destroy();
        }
    }
}

// 导出测试类
export { BeautyTest };
