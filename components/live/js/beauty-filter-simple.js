/**
 * 简化版美颜滤镜处理模块
 * 专注于兼容性和稳定性，使用Canvas 2D实现
 */

class SimpleBeautyFilter {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.isEnabled = false;
        
        // 美颜参数
        this.settings = {
            smoothing: 0.5,      // 磨皮强度 0-1
            whitening: 0.3,      // 美白强度 0-1
            brightness: 0.1,     // 亮度调节 -1到1
            contrast: 0.1,       // 对比度调节 -1到1
            saturation: 0.1,     // 饱和度调节 -1到1
            sharpening: 0.2      // 锐化强度 0-1
        };
    }
    
    /**
     * 初始化Canvas
     */
    initCanvas(width, height) {
        try {
            this.canvas = document.createElement('canvas');
            this.canvas.width = width;
            this.canvas.height = height;
            this.ctx = this.canvas.getContext('2d');
            
            if (!this.ctx) {
                console.error('无法获取Canvas 2D上下文');
                return false;
            }
            
            return true;
        } catch (error) {
            console.error('Canvas初始化失败:', error);
            return false;
        }
    }
    
    /**
     * 处理视频帧
     */
    processFrame(video) {
        if (!this.isEnabled || !this.canvas || !this.ctx) {
            return video;
        }
        
        try {
            // 绘制原始视频帧
            this.ctx.drawImage(video, 0, 0, this.canvas.width, this.canvas.height);
            
            // 应用美颜效果
            this.applyBeautyEffects();
            
            return this.canvas;
        } catch (error) {
            console.error('美颜处理失败:', error);
            return video;
        }
    }
    
    /**
     * 应用美颜效果
     */
    applyBeautyEffects() {
        if (!this.ctx) return;
        
        try {
            // 获取图像数据
            const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
            const data = imageData.data;
            
            // 应用各种美颜效果
            for (let i = 0; i < data.length; i += 4) {
                let r = data[i];
                let g = data[i + 1];
                let b = data[i + 2];
                
                // 美白效果
                if (this.settings.whitening > 0) {
                    const whitening = this.settings.whitening * 30;
                    r = Math.min(255, r + whitening);
                    g = Math.min(255, g + whitening);
                    b = Math.min(255, b + whitening);
                }
                
                // 亮度调节
                if (this.settings.brightness !== 0) {
                    const brightness = this.settings.brightness * 50;
                    r = Math.max(0, Math.min(255, r + brightness));
                    g = Math.max(0, Math.min(255, g + brightness));
                    b = Math.max(0, Math.min(255, b + brightness));
                }
                
                // 对比度调节
                if (this.settings.contrast !== 0) {
                    const contrast = 1 + this.settings.contrast;
                    r = Math.max(0, Math.min(255, (r - 128) * contrast + 128));
                    g = Math.max(0, Math.min(255, (g - 128) * contrast + 128));
                    b = Math.max(0, Math.min(255, (b - 128) * contrast + 128));
                }
                
                // 饱和度调节
                if (this.settings.saturation !== 0) {
                    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                    const saturation = 1 + this.settings.saturation;
                    r = Math.max(0, Math.min(255, gray + (r - gray) * saturation));
                    g = Math.max(0, Math.min(255, gray + (g - gray) * saturation));
                    b = Math.max(0, Math.min(255, gray + (b - gray) * saturation));
                }
                
                // 更新像素值
                data[i] = r;
                data[i + 1] = g;
                data[i + 2] = b;
            }
            
            // 磨皮效果（简单模糊）
            if (this.settings.smoothing > 0) {
                this.applySmoothingEffect(imageData);
            }
            
            // 应用处理后的图像数据
            this.ctx.putImageData(imageData, 0, 0);
            
        } catch (error) {
            console.error('美颜效果应用失败:', error);
        }
    }
    
    /**
     * 应用磨皮效果
     */
    applySmoothingEffect(imageData) {
        if (this.settings.smoothing <= 0) return;
        
        try {
            const data = imageData.data;
            const width = imageData.width;
            const height = imageData.height;
            const smoothing = this.settings.smoothing;
            
            // 创建临时数组存储原始数据
            const originalData = new Uint8ClampedArray(data);
            
            // 简单的3x3平均滤波器
            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    const idx = (y * width + x) * 4;
                    
                    let r = 0, g = 0, b = 0;
                    let count = 0;
                    
                    // 3x3邻域
                    for (let dy = -1; dy <= 1; dy++) {
                        for (let dx = -1; dx <= 1; dx++) {
                            const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
                            r += originalData[neighborIdx];
                            g += originalData[neighborIdx + 1];
                            b += originalData[neighborIdx + 2];
                            count++;
                        }
                    }
                    
                    // 平均值
                    r /= count;
                    g /= count;
                    b /= count;
                    
                    // 混合原始值和模糊值
                    data[idx] = originalData[idx] * (1 - smoothing) + r * smoothing;
                    data[idx + 1] = originalData[idx + 1] * (1 - smoothing) + g * smoothing;
                    data[idx + 2] = originalData[idx + 2] * (1 - smoothing) + b * smoothing;
                }
            }
        } catch (error) {
            console.error('磨皮效果应用失败:', error);
        }
    }
    
    /**
     * 更新美颜设置
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
    }
    
    /**
     * 启用/禁用美颜
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
    }
    
    /**
     * 获取当前设置
     */
    getSettings() {
        return { ...this.settings };
    }
    
    /**
     * 重置设置为默认值
     */
    resetSettings() {
        this.settings = {
            smoothing: 0.5,
            whitening: 0.3,
            brightness: 0.1,
            contrast: 0.1,
            saturation: 0.1,
            sharpening: 0.2
        };
    }
    
    /**
     * 销毁资源
     */
    destroy() {
        this.canvas = null;
        this.ctx = null;
    }
}

// 导出简化版美颜滤镜类
export { SimpleBeautyFilter };
