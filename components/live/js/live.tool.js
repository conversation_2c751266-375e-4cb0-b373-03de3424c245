/**
 *
 * @param id 父容器ID
 * @param {Object} options 视频选项
 * @returns {HTMLVideoElement}
 */
function createVideo(id, options = {}) {
    const container = document.getElementById(id);
    if (!container) {
        console.error(`容器元素 ${id} 不存在`);
        return null;
    }

    const video = document.createElement('video');
    video.id = `${id}-video`;
    video.autoplay = true;
    video.muted = true;
    video.playsInline = true; // 注意：H5 中是 playsInline（小写 i）
    container.appendChild(video);

    // 应用默认样式，除非提供了自定义样式
    video.style.objectFit = options.objectFit || 'cover'; // 默认改为cover
    video.style.width = options.width || '100%';
    video.style.height = options.height || '100%'; // 改为100%而不是auto

    // 如果需要，添加绝对定位
    if (options.position === 'absolute') {
        video.style.position = 'absolute';
        video.style.top = '0';
        video.style.left = '0';
    }

    return video;
}

/**
 * 删除视频元素
 * @param id 视频元素ID或父容器ID
 * @param isContainer 是否为父容器ID
 * @returns {boolean}
 */
function removeVideo(id, isContainer = false) {
    const videoId = isContainer ? `${id}-video` : id;
    const video = document.getElementById(videoId);

    if (!video) {
        console.error(`视频元素 ${videoId} 不存在`);
        return false;
    }

    // 如果有媒体流，先停止所有轨道
    if (video.srcObject) {
        try {
            const tracks = video.srcObject.getTracks();
            tracks.forEach(track => track.stop());
        } catch (e) {
            console.error('停止视频轨道失败:', e);
        }
    }

    // 移除元素
    video.parentNode.removeChild(video);
    return true;
}

/**
 * 获取视频源对象流
 * @param facingMode 摄像头朝向 user 表示前置摄像头，environment 表示后置摄像头
 * @param width 视频宽度
 * @returns {Promise<MediaStream>}
 */
function getVideoSrcObjectStream(facingMode = 'user', width = 720) {
    return new Promise((resolve, reject) => {

        // 检查浏览器是否支持getUserMedia
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            console.error('浏览器不支持getUserMedia API');
            uni.showToast({
                title: this.$t('您的浏览器不支持摄像头访问'), icon: 'none'
            });
            reject("您的浏览器不支持摄像头访问,浏览器不支持getUserMedia");
            return;
        }

        const constraints = {
            audio: true,
            video: {
                width: { ideal: width, max: 1080 },
                facingMode: facingMode,
            }
        };


        // 获取媒体流
        navigator.mediaDevices.getUserMedia(constraints).then(stream => {
            resolve(stream)
        }).catch(error => {
            console.log("navigator.mediaDevices.getUserMedia:error")
            console.error("getUserMedia error:", error.name, error.message);

            // 如果是 OverconstrainedError，可以进一步检查 constraint 问题
            if (error.name === 'OverconstrainedError') {
                console.error("Constraint problem:", error.constraint);
            }
        });

    });

}

/**
 * 检查字符串是否为有效的URL
 * @param {String} str - 要检查的字符串
 * @return {Boolean} 是否为有效URL
 */
function isValidUrl(str) {
    if (!str) return false;

    // 检查是否为字符串
    if (typeof str !== 'string') return false;

    // 使用简化的URL验证方法
    try {
        // 检查是否以http或https开头
        return str.startsWith('http://') || str.startsWith('https://') || str.startsWith('//');
    } catch (e) {
        console.error('URL验证错误:', e);
        return false;
    }
}


/**
 * 创建带美颜功能的视频流
 * @param {MediaStream} originalStream 原始视频流
 * @param {Object} beautyFilter 美颜滤镜实例
 * @returns {MediaStream} 处理后的视频流
 */
function createBeautyStream(originalStream, beautyFilter) {
    if (!originalStream || !beautyFilter) {
        return originalStream;
    }

    try {
        // 获取视频轨道
        const videoTrack = originalStream.getVideoTracks()[0];
        if (!videoTrack) {
            return originalStream;
        }

        // 创建视频元素用于处理
        const video = document.createElement('video');
        video.srcObject = originalStream;
        video.autoplay = true;
        video.muted = true;
        video.playsInline = true;

        // 等待视频加载完成
        return new Promise((resolve) => {
            video.addEventListener('loadedmetadata', () => {
                const width = video.videoWidth;
                const height = video.videoHeight;

                // 初始化美颜滤镜的Canvas
                beautyFilter.initCanvas(width, height);

                // 创建Canvas流
                const canvas = beautyFilter.canvas;
                const canvasStream = canvas.captureStream(30); // 30fps

                // 替换音频轨道
                const audioTracks = originalStream.getAudioTracks();
                audioTracks.forEach(track => {
                    canvasStream.addTrack(track);
                });

                // 开始处理视频帧
                const processFrame = () => {
                    if (video.readyState >= 2) { // HAVE_CURRENT_DATA
                        beautyFilter.processFrame(video);
                    }
                    requestAnimationFrame(processFrame);
                };
                processFrame();

                resolve(canvasStream);
            });
        });

    } catch (error) {
        console.error('创建美颜流失败:', error);
        return originalStream;
    }
}

/**
 * 获取带美颜功能的视频源对象流
 * @param facingMode 摄像头朝向 user 表示前置摄像头，environment 表示后置摄像头
 * @param width 视频宽度
 * @param beautyFilter 美颜滤镜实例（可选）
 * @returns {Promise<MediaStream>}
 */
function getBeautyVideoSrcObjectStream(facingMode = 'user', width = 720, beautyFilter = null) {
    return new Promise(async (resolve, reject) => {
        try {
            // 先获取原始视频流
            const originalStream = await getVideoSrcObjectStream(facingMode, width);

            // 如果有美颜滤镜且已启用，则处理视频流
            if (beautyFilter && beautyFilter.isEnabled) {
                const beautyStream = await createBeautyStream(originalStream, beautyFilter);
                resolve(beautyStream);
            } else {
                resolve(originalStream);
            }
        } catch (error) {
            reject(error);
        }
    });
}

/**
 * 更新视频流的美颜效果
 * @param {HTMLVideoElement} videoElement 视频元素
 * @param {MediaStream} originalStream 原始视频流
 * @param {Object} beautyFilter 美颜滤镜实例
 * @returns {Promise<MediaStream>} 更新后的视频流
 */
function updateBeautyStream(videoElement, originalStream, beautyFilter) {
    return new Promise(async (resolve) => {
        try {
            if (beautyFilter && beautyFilter.isEnabled) {
                const beautyStream = await createBeautyStream(originalStream, beautyFilter);
                if (videoElement) {
                    videoElement.srcObject = beautyStream;
                }
                resolve(beautyStream);
            } else {
                if (videoElement) {
                    videoElement.srcObject = originalStream;
                }
                resolve(originalStream);
            }
        } catch (error) {
            console.error('更新美颜流失败:', error);
            resolve(originalStream);
        }
    });
}

// 添加 ES6 模块导出
export {
    createVideo,
    getVideoSrcObjectStream,
    removeVideo,
    isValidUrl,
    createBeautyStream,
    getBeautyVideoSrcObjectStream,
    updateBeautyStream
};
