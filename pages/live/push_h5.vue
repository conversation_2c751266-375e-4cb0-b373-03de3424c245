<template>
  <view class="live-container">

    <view v-if="roomInfo && roomInfo.id>0">
      <!-- 视频预览区域 -->
      <publisher ref="publisher" :room-id="roomInfo.id" :is-push="isPush" :display="roomInfo.userId"></publisher>

      <!-- 顶部导航栏 -->
      <top-bar :status-bar-height="statusBarHeight" :room-info="roomInfo" :is-push="isPush"></top-bar>

      <!--  直播间设置  -->
      <settings-popup :room-info="roomInfo"></settings-popup>

      <!--  美颜设置  -->
      <beauty-settings ref="beautySettings"></beauty-settings>

      <!--  聊天框  -->
      <live-chat page-type="push" :room-info="roomInfo" :is-push="isPush"></live-chat>

      <!--   礼物墙、礼物统计   -->
      <stats-panel :status-bar-height="statusBarHeight" :room-info="roomInfo"></stats-panel>

      <!--  礼物面板  -->
      <live-gift :room-id="roomInfo.id" :room-user-id="roomInfo.userId"></live-gift>

      <!-- 点赞动画   -->
      <like-animation></like-animation>

      <participants :room-info="roomInfo"></participants>

      <!-- 观看用户列表弹窗 -->
      <viewers-list ref="viewersList" :room-id="roomInfo.id"></viewers-list>
    </view>

    <!-- 底部控制栏 -->
    <bottom-controls :is-push="isPush"></bottom-controls>

  </view>
</template>

<script>
import {
  AppsLiveApiLiveRoomsApiDetail,
  AppsLiveApiLiveRoomsApiStartSocketOn,
  AppsLiveApiLiveRoomsApiOfflineSocketOn,
  AppsLiveApiLiveRoomsApiStartSocketOff,
  AppsLiveApiLiveRoomsApiOfflineSocketOff
} from '@/network/api/live-rooms.ts';
import i18n from "@/Lib/i18n";
import Publisher from "@/components/live/publisher";
import BottomControls from "@/components/live/BottomControls";
import SettingsPopup from "@/components/live/SettingsPopup";
import TopBar from "@/components/live/TopBar";
import LiveChat from "@/components/live/LiveChat";
import LiveGift from "@/components/live/LiveGift";
import LikeAnimation from "@/components/live/LikeAnimation";
import {Socket} from "@/Lib/Socket";
import StatsPanel from "@/components/live/StatsPanel";
import ViewersList from "@/components/live/ViewersList.vue";
import {User} from "@/Lib/User";
import Participants from "@/components/live/participants.vue";

export default {
  components: {
    Participants,
    StatsPanel, LiveChat, TopBar, SettingsPopup, BottomControls, Publisher, LikeAnimation, LiveGift, ViewersList
  },
  data() {
    return {
      // 房间信息
      roomId: 0,
      roomInfo: {
        user: {},
        title: '',
        statistics: {
          totalViewers: 0
        },
        allowComments: 1,
        allowGifts: 1,

      },
      statusBarHeight: 0,

      isPush: true,// 是否是主播， false 是连麦进入的
    }
  },
  onUnload() {
    // 清理WebSocket监听
    AppsLiveApiLiveRoomsApiStartSocketOff(this.SocketOnStart);
    AppsLiveApiLiveRoomsApiOfflineSocketOff(this.SocketOnOffline);
    // 清理事件监听
    uni.$off('openViewersList', this.handleOpenViewersList);
  },
  onLoad(opt) {
    this.roomId = parseInt(opt.roomId) || 0;
    const token = opt.token;
    const userId = opt.userId;
    if (opt.isPush === 'false') {
      this.isPush = false;
    }
    this.statusBarHeight = parseInt(opt.statusBarHeight);
    if (!this.roomId || !token) {
      uni.showModal({
        title: this.$t('提示'),
        content: this.$t('请选择直播间'),
        showCancel: false,
        confirmText: this.$t('确定'),
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack();
          }
        }
      })
      return;
    }

    User.setLongToken(token);
    User.setUserId(userId);

    // 获取直播间设置
    this.fetchRoomSettings();

    // 监听开播和下播事件
    AppsLiveApiLiveRoomsApiStartSocketOn(true, this.SocketOnStart);
    AppsLiveApiLiveRoomsApiOfflineSocketOn(true, this.SocketOnOffline);

    // 监听打开观看用户列表事件
    uni.$on('openViewersList', this.handleOpenViewersList);

    // 连接WebSocket
    Socket.getInstance().connect({
      roomId: this.roomId
    })
  },
  onReady() {
    // 创建视频预览元素
    this.$nextTick(() => {
      // 禁止页面滚动回弹
      this.preventBounce();
    });
  },
  onShow() {
    Socket.getInstance().ping()
  },
  methods: {
    $t(text) {
      return i18n.t(text);
    },

    // 禁止页面滚动回弹
    preventBounce() {
      // 阻止整个文档的触摸滑动默认行为
      document.addEventListener('touchmove', function (e) {
        e.preventDefault();
      }, {passive: false});

      // 对于iOS设备的特殊处理
      if (/iphone|ipad|ipod/i.test(navigator.userAgent.toLowerCase())) {
        // 设置body和html样式
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.width = '100%';
        document.body.style.height = '100%';
        document.documentElement.style.overflow = 'hidden';
      }
    },


    // 获取直播间设置
    fetchRoomSettings() {
      if (!this.roomId) return;


      AppsLiveApiLiveRoomsApiDetail({
        id: this.roomId
      }).then(res => {
        console.log('获取房间设置成功:', res);
        if (res) {
          this.roomInfo = res;
          // 更新房间信息
        }
      }).catch(err => {
        console.error('获取房间设置失败:', err);
        uni.showToast({
          title: this.$t('获取设置失败'),
          icon: 'none'
        });
      });
    },


    // WebSocket监听 - 开播成功
    SocketOnStart(res) {
      console.log("开播成功事件:", JSON.stringify(res));
    },

    // WebSocket监听 - 下播
    SocketOnOffline(res) {
      console.log("下播成功事件:", JSON.stringify(res));
    },

    /**
     * 处理打开观看用户列表事件
     */
    handleOpenViewersList(roomId) {
      if (this.$refs.viewersList && roomId) {
        this.$refs.viewersList.open();
      }
    },

  }
}
</script>

<style lang="scss">
/* 禁止页面回弹样式 */
page {
  overflow: hidden;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: #2a2a2a;
}


</style>
