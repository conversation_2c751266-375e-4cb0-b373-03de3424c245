<template>
  <view class="beauty-test">
    <view class="header">
      <text class="title">美颜功能测试</text>
    </view>
    
    <view class="video-container">
      <video 
        id="test-video" 
        ref="testVideo"
        autoplay 
        muted 
        playsinline
        class="test-video"
      ></video>
    </view>
    
    <view class="controls">
      <button @click="startCamera" :disabled="isStarted" class="btn primary">
        {{ isStarted ? '摄像头已启动' : '启动摄像头' }}
      </button>
      
      <button @click="toggleBeauty" :disabled="!isStarted" class="btn">
        {{ beautyEnabled ? '关闭美颜' : '开启美颜' }}
      </button>
      
      <button @click="runTest" :disabled="!isStarted" class="btn">
        运行测试
      </button>
    </view>
    
    <view class="settings" v-if="beautyEnabled">
      <view class="setting-item">
        <text>磨皮: {{ Math.round(beautySettings.smoothing * 100) }}%</text>
        <slider 
          :value="beautySettings.smoothing * 100"
          @change="updateSetting('smoothing', $event)"
          min="0" max="100" step="1"
        />
      </view>
      
      <view class="setting-item">
        <text>美白: {{ Math.round(beautySettings.whitening * 100) }}%</text>
        <slider 
          :value="beautySettings.whitening * 100"
          @change="updateSetting('whitening', $event)"
          min="0" max="100" step="1"
        />
      </view>
      
      <view class="setting-item">
        <text>亮度: {{ Math.round((beautySettings.brightness + 1) * 50) }}%</text>
        <slider 
          :value="(beautySettings.brightness + 1) * 50"
          @change="updateSetting('brightness', $event, true)"
          min="0" max="100" step="1"
        />
      </view>
    </view>
    
    <view class="test-results" v-if="testResults.length > 0">
      <text class="results-title">测试结果:</text>
      <view 
        v-for="(result, index) in testResults" 
        :key="index"
        class="result-item"
        :class="{ passed: result.passed, failed: !result.passed }"
      >
        <text class="result-name">{{ result.name }}</text>
        <text class="result-status">{{ result.passed ? '✅ 通过' : '❌ 失败' }}</text>
        <text class="result-message">{{ result.message }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { SimpleBeautyFilter } from "@/components/live/js/beauty-filter-simple";
import { BeautyTest } from "@/components/live/js/beauty-test";

export default {
  name: "BeautyTest",
  data() {
    return {
      isStarted: false,
      beautyEnabled: false,
      beautyFilter: null,
      originalStream: null,
      beautyStream: null,
      testResults: [],
      beautySettings: {
        smoothing: 0.5,
        whitening: 0.3,
        brightness: 0.1,
        contrast: 0.1,
        saturation: 0.1,
        sharpening: 0.2
      }
    }
  },
  mounted() {
    this.beautyFilter = new SimpleBeautyFilter();
  },
  methods: {
    async startCamera() {
      try {
        this.originalStream = await navigator.mediaDevices.getUserMedia({
          video: { width: 640, height: 480 },
          audio: false
        });
        
        this.$refs.testVideo.srcObject = this.originalStream;
        this.isStarted = true;
        
        uni.showToast({
          title: '摄像头启动成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('摄像头启动失败:', error);
        uni.showToast({
          title: '摄像头启动失败',
          icon: 'error'
        });
      }
    },
    
    async toggleBeauty() {
      this.beautyEnabled = !this.beautyEnabled;
      
      if (this.beautyEnabled) {
        await this.enableBeauty();
      } else {
        await this.disableBeauty();
      }
    },
    
    async enableBeauty() {
      try {
        if (!this.beautyFilter || !this.originalStream) return;
        
        // 创建视频元素用于美颜处理
        const video = document.createElement('video');
        video.srcObject = this.originalStream;
        video.autoplay = true;
        video.muted = true;
        video.playsInline = true;
        
        // 等待视频加载
        await new Promise((resolve) => {
          video.addEventListener('loadedmetadata', resolve);
        });
        
        // 初始化美颜滤镜
        const success = this.beautyFilter.initCanvas(video.videoWidth, video.videoHeight);
        if (!success) {
          throw new Error('美颜滤镜初始化失败');
        }
        
        this.beautyFilter.setEnabled(true);
        this.beautyFilter.updateSettings(this.beautySettings);
        
        // 创建Canvas流
        const canvas = this.beautyFilter.canvas;
        this.beautyStream = canvas.captureStream(30);
        
        // 开始处理视频帧
        const processFrame = () => {
          if (this.beautyEnabled && video.readyState >= 2) {
            this.beautyFilter.processFrame(video);
          }
          if (this.beautyEnabled) {
            requestAnimationFrame(processFrame);
          }
        };
        processFrame();
        
        // 更新视频源
        this.$refs.testVideo.srcObject = this.beautyStream;
        
        uni.showToast({
          title: '美颜已开启',
          icon: 'success'
        });
      } catch (error) {
        console.error('美颜开启失败:', error);
        this.beautyEnabled = false;
        uni.showToast({
          title: '美颜开启失败',
          icon: 'error'
        });
      }
    },
    
    async disableBeauty() {
      this.beautyFilter.setEnabled(false);
      this.$refs.testVideo.srcObject = this.originalStream;
      
      uni.showToast({
        title: '美颜已关闭',
        icon: 'success'
      });
    },
    
    updateSetting(key, event, isBipolar = false) {
      let value = event.detail.value / 100;
      
      if (isBipolar) {
        value = (value * 2) - 1;
      }
      
      this.beautySettings[key] = value;
      
      if (this.beautyFilter) {
        this.beautyFilter.updateSettings(this.beautySettings);
      }
    },
    
    async runTest() {
      try {
        const beautyTest = new BeautyTest();
        this.testResults = await beautyTest.runAllTests();
        beautyTest.cleanup();
        
        uni.showToast({
          title: '测试完成',
          icon: 'success'
        });
      } catch (error) {
        console.error('测试运行失败:', error);
        uni.showToast({
          title: '测试运行失败',
          icon: 'error'
        });
      }
    }
  },
  
  beforeDestroy() {
    if (this.originalStream) {
      this.originalStream.getTracks().forEach(track => track.stop());
    }
    if (this.beautyFilter) {
      this.beautyFilter.destroy();
    }
  }
}
</script>

<style scoped lang="scss">
.beauty-test {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  
  .title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
}

.video-container {
  width: 100%;
  height: 400rpx;
  background-color: #000;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  
  .test-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.controls {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
  flex-wrap: wrap;
  
  .btn {
    flex: 1;
    min-width: 200rpx;
    padding: 20rpx;
    border-radius: 10rpx;
    border: none;
    font-size: 16px;
    
    &.primary {
      background-color: #007aff;
      color: white;
    }
    
    &:not(.primary) {
      background-color: #fff;
      color: #333;
      border: 1rpx solid #ddd;
    }
    
    &:disabled {
      opacity: 0.5;
    }
  }
}

.settings {
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  
  .setting-item {
    margin-bottom: 30rpx;
    
    text {
      display: block;
      margin-bottom: 15rpx;
      font-size: 16px;
      color: #333;
    }
  }
}

.test-results {
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
  
  .results-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }
  
  .result-item {
    padding: 20rpx;
    margin-bottom: 15rpx;
    border-radius: 8rpx;
    
    &.passed {
      background-color: #f0f9ff;
      border-left: 4rpx solid #10b981;
    }
    
    &.failed {
      background-color: #fef2f2;
      border-left: 4rpx solid #ef4444;
    }
    
    .result-name {
      font-weight: bold;
      display: block;
      margin-bottom: 5rpx;
    }
    
    .result-status {
      font-size: 14px;
      display: block;
      margin-bottom: 5rpx;
    }
    
    .result-message {
      font-size: 12px;
      color: #666;
      display: block;
    }
  }
}
</style>
