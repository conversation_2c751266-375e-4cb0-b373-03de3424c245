{"name": "live-h5", "version": "1.0.0", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build-proto:pbjs": "pbjs --dependency   protobufjs/minimal.js --es6    --no-servic --no-beautify   --no-create  --no-delimited  --no-typeurl  --no-verify --target static-module --wrap es6 --out ./proto/proto.js ./proto/protos/*.proto", "build-proto:pbts": "pbts  --main --out ./proto/proto.d.ts ./proto/*.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@mediapipe/camera_utils": "^0.3.**********", "@mediapipe/drawing_utils": "^0.3.**********", "@mediapipe/face_mesh": "^0.4.**********", "@mediapipe/selfie_segmentation": "^0.1.**********", "protobufjs": "^7.5.0", "protobufjs-cli": "^1.1.3"}}